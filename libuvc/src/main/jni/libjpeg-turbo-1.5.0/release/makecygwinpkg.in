#!/bin/sh

set -u
set -e
trap onexit INT
trap onexit TERM
trap onexit EXIT

TMPDIR=

onexit()
{
	if [ ! "$TMPDIR" = "" ]; then
		rm -rf $TMPDIR
	fi
}

PACKAGE_NAME=@PKGNAME@
VERSION=@VERSION@
BUILD=@BUILD@
SRCDIR=@abs_top_srcdir@

PREFIX=%{__prefix}
DOCDIR=%{__docdir}
LIBDIR=%{__libdir}

umask 022
rm -f $PACKAGE_NAME-$VERSION-$BUILD.tar.bz2
TMPDIR=`mktemp -d /tmp/ljtbuild.XXXXXX`
__PWD=`pwd`
make install DESTDIR=$TMPDIR/pkg docdir=/usr/share/doc/$PACKAGE_NAME-$VERSION \
	exampledir=/usr/share/doc/$PACKAGE_NAME-$VERSION
rm $TMPDIR/pkg$LIBDIR/*.la
if [ "$PREFIX" = "/opt/libjpeg-turbo" -a "$DOCDIR" = "/opt/libjpeg-turbo/doc" ]; then
	ln -fs /usr/share/doc/$PACKAGE_NAME-$VERSION $TMPDIR/pkg$DOCDIR
fi
cd $TMPDIR/pkg
tar cfj ../$PACKAGE_NAME-$VERSION-$BUILD.tar.bz2 *
cd $__PWD
mv $TMPDIR/*.tar.bz2 .

exit 0
