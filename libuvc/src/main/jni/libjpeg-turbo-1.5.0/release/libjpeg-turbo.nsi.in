!include x64.nsh
Name "@CMAKE_PROJECT_NAME@ SDK for @INST_PLATFORM@"
OutFile "@CMAKE_BINARY_DIR@\${BUILDDIR}@INST_NAME@.exe"
InstallDir @INST_DIR@

SetCompressor bzip2

Page directory
Page instfiles

UninstPage uninstConfirm
UninstPage instfiles

Section "@CMAKE_PROJECT_NAME@ SDK for @INST_PLATFORM@ (required)"
!ifdef WIN64
	${If} ${RunningX64}
	${DisableX64FSRedirection}
	${Endif}
!endif
	SectionIn RO
!ifdef GCC
	IfFileExists $SYSDIR/libturbojpeg.dll exists 0
!else
	IfFileExists $SYSDIR/turbojpeg.dll exists 0
!endif
	goto notexists
	exists:
!ifdef GCC
	MessageBox MB_OK "An existing version of the @CMAKE_PROJECT_NAME@ SDK for @INST_PLATFORM@ is already installed.  Please uninstall it first."
!else
	MessageBox MB_OK "An existing version of the @CMAKE_PROJECT_NAME@ SDK for @INST_PLATFORM@ or the TurboJPEG SDK is already installed.  Please uninstall it first."
!endif
	quit

	notexists:
	SetOutPath $SYSDIR
!ifdef GCC
	File "@CMAKE_BINARY_DIR@\libturbojpeg.dll"
!else
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}turbojpeg.dll"
!endif
	SetOutPath $INSTDIR\bin
!ifdef GCC
	File "@CMAKE_BINARY_DIR@\libturbojpeg.dll"
!else
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}turbojpeg.dll"
!endif
!ifdef GCC
	File "/oname=libjpeg-@DLL_VERSION@.dll" "@CMAKE_BINARY_DIR@\sharedlib\libjpeg-*.dll"
!else
	File "@CMAKE_BINARY_DIR@\sharedlib\${BUILDDIR}src.jpeg@DLL_VERSION@.dll"
!endif
	File "@CMAKE_BINARY_DIR@\sharedlib\${BUILDDIR}cjpeg.exe"
	File "@CMAKE_BINARY_DIR@\sharedlib\${BUILDDIR}djpeg.exe"
	File "@CMAKE_BINARY_DIR@\sharedlib\${BUILDDIR}jpegtran.exe"
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}tjbench.exe"
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}rdjpgcom.exe"
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}wrjpgcom.exe"
	SetOutPath $INSTDIR\lib
!ifdef GCC
	File "@CMAKE_BINARY_DIR@\libturbojpeg.dll.a"
	File "@CMAKE_BINARY_DIR@\libturbojpeg.a"
	File "@CMAKE_BINARY_DIR@\sharedlib\libjpeg.dll.a"
	File "@CMAKE_BINARY_DIR@\libjpeg.a"
!else
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}turbojpeg.lib"
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}turbojpeg-static.lib"
	File "@CMAKE_BINARY_DIR@\sharedlib\${BUILDDIR}src.jpeg.lib"
	File "@CMAKE_BINARY_DIR@\${BUILDDIR}src.jpeg-static.lib"
!endif
!ifdef JAVA
	SetOutPath $INSTDIR\classes
	File "@CMAKE_BINARY_DIR@\java\${BUILDDIR}turbojpeg.jar"
!endif
	SetOutPath $INSTDIR\include
	File "@CMAKE_BINARY_DIR@\jconfig.h"
	File "@CMAKE_SOURCE_DIR@\jerror.h"
	File "@CMAKE_SOURCE_DIR@\jmorecfg.h"
	File "@CMAKE_SOURCE_DIR@\jpeglib.h"
	File "@CMAKE_SOURCE_DIR@\turbojpeg.h"
	SetOutPath $INSTDIR\doc
	File "@CMAKE_SOURCE_DIR@\README.ijg"
	File "@CMAKE_SOURCE_DIR@\README.md"
	File "@CMAKE_SOURCE_DIR@\LICENSE.md"
	File "@CMAKE_SOURCE_DIR@\example.c"
	File "@CMAKE_SOURCE_DIR@\libjpeg.txt"
	File "@CMAKE_SOURCE_DIR@\structure.txt"
	File "@CMAKE_SOURCE_DIR@\usage.txt"
	File "@CMAKE_SOURCE_DIR@\wizard.txt"

	WriteRegStr HKLM "SOFTWARE\@INST_REG_NAME@ @VERSION@" "Install_Dir" "$INSTDIR"

	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\@INST_REG_NAME@ @VERSION@" "DisplayName" "@CMAKE_PROJECT_NAME@ SDK v@VERSION@ for @INST_PLATFORM@"
	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\@INST_REG_NAME@ @VERSION@" "UninstallString" '"$INSTDIR\uninstall_@VERSION@.exe"'
	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\@INST_REG_NAME@ @VERSION@" "NoModify" 1
	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\@INST_REG_NAME@ @VERSION@" "NoRepair" 1
	WriteUninstaller "uninstall_@VERSION@.exe"
SectionEnd

Section "Uninstall"
!ifdef WIN64
	${If} ${RunningX64}
	${DisableX64FSRedirection}
	${Endif}
!endif

	SetShellVarContext all

	DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\@INST_REG_NAME@ @VERSION@"
	DeleteRegKey HKLM "SOFTWARE\@INST_REG_NAME@ @VERSION@"

!ifdef GCC
	Delete $INSTDIR\bin\libjpeg-@DLL_VERSION@.dll
	Delete $INSTDIR\bin\libturbojpeg.dll
	Delete $SYSDIR\libturbojpeg.dll
	Delete $INSTDIR\lib\libturbojpeg.dll.a"
	Delete $INSTDIR\lib\libturbojpeg.a"
	Delete $INSTDIR\lib\libjpeg.dll.a"
	Delete $INSTDIR\lib\libjpeg.a"
!else
	Delete $INSTDIR\bin\src.jpeg@DLL_VERSION@.dll
	Delete $INSTDIR\bin\turbojpeg.dll
	Delete $SYSDIR\turbojpeg.dll
	Delete $INSTDIR\lib\src.jpeg.lib
	Delete $INSTDIR\lib\src.jpeg-static.lib
	Delete $INSTDIR\lib\turbojpeg.lib
	Delete $INSTDIR\lib\turbojpeg-static.lib
!endif
!ifdef JAVA
	Delete $INSTDIR\classes\turbojpeg.jar
!endif
	Delete $INSTDIR\bin\cjpeg.exe
	Delete $INSTDIR\bin\djpeg.exe
	Delete $INSTDIR\bin\jpegtran.exe
	Delete $INSTDIR\bin\tjbench.exe
	Delete $INSTDIR\bin\rdjpgcom.exe
	Delete $INSTDIR\bin\wrjpgcom.exe
	Delete $INSTDIR\include\jconfig.h"
	Delete $INSTDIR\include\jerror.h"
	Delete $INSTDIR\include\jmorecfg.h"
	Delete $INSTDIR\include\jpeglib.h"
	Delete $INSTDIR\include\turbojpeg.h"
	Delete $INSTDIR\uninstall_@VERSION@.exe
	Delete $INSTDIR\doc\README.ijg
	Delete $INSTDIR\doc\README.md
	Delete $INSTDIR\doc\LICENSE.md
	Delete $INSTDIR\doc\example.c
	Delete $INSTDIR\doc\libjpeg.txt
	Delete $INSTDIR\doc\structure.txt
	Delete $INSTDIR\doc\usage.txt
	Delete $INSTDIR\doc\wizard.txt

	RMDir "$INSTDIR\include"
	RMDir "$INSTDIR\lib"
	RMDir "$INSTDIR\doc"
!ifdef JAVA
	RMDir "$INSTDIR\classes"
!endif
	RMDir "$INSTDIR\bin"
	RMDir "$INSTDIR"

SectionEnd
