{"permissions": {"allow": ["mcp__Brave<PERSON><PERSON>ch__brave_web_search", "mcp__firecrawl__firecrawl_scrape", "mcp__sequential-thinking__sequentialthinking", "mcp__firecrawl__firecrawl_search", "Bash(./gradlew compileDebugKotlin:*)", "WebFetch(domain:emteria.com)", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:androiderrors.com)", "WebFetch(domain:www.sdgsystems.com)", "<PERSON><PERSON>(./gradlew:*)"], "deny": []}}